import { Component, signal, inject, computed } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { Auth, LoginCredentials } from '../../../core/auth/auth';

@Component({
  selector: 'app-login',
  templateUrl: './login.html',
  styleUrl: './login.scss',
  imports: [
    CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule
  ]
})
export class Login {
  private auth = inject(Auth);

  // Signal-based form state
  protected username = signal('');
  protected password = signal('');
  protected hidePassword = signal(true);

  // Computed validation signals
  protected isFormValid = computed(() =>
    this.username().trim().length > 0 && this.password().trim().length > 0
  );

  // Access auth signals
  protected isLoading = this.auth.isLoading;
  protected error = this.auth.error;

  onSubmit(): void {
    const credentials: LoginCredentials = {
      username: this.username(),
      password: this.password()
    };

    if (credentials.username && credentials.password) {
      this.auth.login(credentials);
    }
  }

  onUsernameChange(value: string): void {
    this.username.set(value);
  }

  onPasswordChange(value: string): void {
    this.password.set(value);
  }

  togglePasswordVisibility(): void {
    this.hidePassword.set(!this.hidePassword());
  }
}
