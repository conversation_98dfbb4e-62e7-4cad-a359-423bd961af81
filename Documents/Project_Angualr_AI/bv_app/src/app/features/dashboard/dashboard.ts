import { Component, signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { httpResource } from '@angular/common/http';
import { Auth } from '../../core/auth/auth';
import { DashboardStats as ApiDashboardStats, RecentActivities } from '../../core/services/dashboard';
import { ResponsiveService } from '../../core/services/responsive.service';
import { ResponsiveContainerComponent } from '../../shared/components/responsive-container/responsive-container.component';
import { LoadingComponent } from '../../shared/components/loading/loading.component';
import { environment } from '../../../environments/environment';

interface DashboardStats {
  totalMaterials: number;
  totalSuppliers: number;
  totalCustomers: number;
  totalOrders: number;
  totalStockValue: number;
  totalProfit: number;
  pendingOrders: number;
  lowStockItems: number;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.scss',
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatGridListModule,
    MatProgressSpinnerModule,
    ResponsiveContainerComponent,
    LoadingComponent
  ]
})
export class Dashboard {
  private auth = inject(Auth);
  private responsiveService = inject(ResponsiveService);

  // Signal to trigger dashboard data loading
  private shouldLoadDashboard = signal(true);

  // Dashboard stats resource
  private dashboardResource = httpResource<ApiDashboardStats>(() =>
    this.shouldLoadDashboard() ? `${environment.apiUrl}/dashboard/stats/` : undefined
  );

  // Computed dashboard stats
  private stats = computed(() => {
    const apiStats = this.dashboardResource.value();
    if (!apiStats) {
      return {
        totalMaterials: 0,
        totalSuppliers: 0,
        totalCustomers: 0,
        totalOrders: 0,
        totalStockValue: 0,
        totalProfit: 0,
        pendingOrders: 0,
        lowStockItems: 0
      };
    }

    return {
      totalMaterials: apiStats.materials.total,
      totalSuppliers: apiStats.suppliers.total,
      totalCustomers: apiStats.customers.total,
      totalOrders: apiStats.customers.pending_orders + apiStats.suppliers.pending_orders,
      totalStockValue: apiStats.financial.total_stock_value,
      totalProfit: apiStats.financial.total_order_value,
      pendingOrders: apiStats.customers.pending_orders,
      lowStockItems: apiStats.materials.low_stock
    };
  });

  // Public readonly signals
  protected readonly dashboardStats = this.stats;
  protected readonly isLoading = this.dashboardResource.isLoading;
  protected readonly user = this.auth.user;

  // Responsive grid columns using ResponsiveService
  protected readonly cols = computed(() => {
    return this.responsiveService.gridColumns();
  });

  // Additional responsive signals
  protected readonly isMobile = computed(() => this.responsiveService.isMobile());
  protected readonly containerClasses = computed(() => this.responsiveService.containerClasses());

  logout(): void {
    this.auth.logout();
  }
}
