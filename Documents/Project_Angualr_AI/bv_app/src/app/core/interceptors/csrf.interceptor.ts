import { HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { switchMap, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { environment } from '../../../environments/environment';

export const csrfInterceptor: HttpInterceptorFn = (req, next) => {
  // Skip CSRF token for GET requests, external URLs, and CSRF endpoint itself
  if (req.method === 'GET' ||
      !req.url.includes(environment.apiBaseUrl) ||
      req.url.includes('/auth/csrf/')) {
    return next(req);
  }

  // Check if CSRF token is already present
  if (req.headers.has('X-CSRFToken')) {
    return next(req);
  }

  // Try to get CSRF token from cookie first
  const csrfTokenFromCookie = getCsrfTokenFromCookie();
  if (csrfTokenFromCookie) {
    const csrfReq = req.clone({
      setHeaders: {
        'X-CSRFToken': csrfTokenFromCookie
      },
      withCredentials: true
    });
    return next(csrfReq);
  }

  const http = inject(HttpClient);

  // Get CSRF token from Django (avoiding interceptor loop)
  return http.get<{csrftoken: string}>(`${environment.apiUrl}/auth/csrf/`, {
    withCredentials: true
  }).pipe(
    switchMap(response => {
      const csrfToken = response.csrftoken;

      // Clone the request and add CSRF token
      const csrfReq = req.clone({
        setHeaders: {
          'X-CSRFToken': csrfToken
        },
        withCredentials: true
      });

      return next(csrfReq);
    }),
    catchError(error => {
      console.error('Failed to get CSRF token:', error);
      // Continue with original request if CSRF token fetch fails
      const credentialsReq = req.clone({
        withCredentials: true
      });
      return next(credentialsReq);
    })
  );
};

function getCsrfTokenFromCookie(): string | null {
  const name = 'csrftoken';
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}
