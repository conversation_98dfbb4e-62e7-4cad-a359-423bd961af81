import { Injectable, signal, computed, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { catchError, tap } from 'rxjs/operators';
import { of } from 'rxjs';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  success: boolean;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class Auth {
  private http = inject(HttpClient);
  private router = inject(Router);

  // Signal-based state management
  private userSignal = signal<User | null>(null);
  private isLoadingSignal = signal<boolean>(false);
  private errorSignal = signal<string | null>(null);

  // Public readonly signals
  readonly user = this.userSignal.asReadonly();
  readonly isAuthenticated = computed(() => this.userSignal() !== null);
  readonly isLoading = this.isLoadingSignal.asReadonly();
  readonly error = this.errorSignal.asReadonly();

  constructor() {
    // Initial auth status check
    this.checkAuthStatus();
  }

  login(credentials: LoginCredentials): void {
    console.log('🔐 Login called with credentials:', credentials);
    console.log('🌐 API URL:', `${environment.apiUrl}/auth/login/`);
    this.isLoadingSignal.set(true);
    this.errorSignal.set(null);

    // Create the request with detailed logging
    const request$ = this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login/`, credentials, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      },
      observe: 'response' // Get full response including headers
    });

    console.log('🚀 Making HTTP request...');

    request$.pipe(
      tap(response => {
        console.log('✅ Full HTTP response:', response);
        console.log('✅ Response status:', response.status);
        console.log('✅ Response headers:', response.headers);
        console.log('✅ Response body:', response.body);

        const body = response.body;
        if (body?.success && body.user) {
          console.log('🎉 Login successful, setting user:', body.user);
          this.userSignal.set(body.user);
          this.router.navigate(['/dashboard']);
        } else {
          console.log('❌ Login failed:', body?.message);
          this.errorSignal.set(body?.message || 'Login failed');
        }
        this.isLoadingSignal.set(false);
      }),
      catchError(error => {
        console.error('🚨 Login error:', error);
        console.error('🚨 Error status:', error.status);
        console.error('🚨 Error statusText:', error.statusText);
        console.error('🚨 Error url:', error.url);
        console.error('🚨 Error headers:', error.headers);
        console.error('🚨 Error error:', error.error);
        console.error('🚨 Error type:', typeof error);
        console.error('🚨 Error constructor:', error.constructor.name);
        this.errorSignal.set(error.error?.message || 'Login failed');
        this.isLoadingSignal.set(false);
        return of(null);
      })
    ).subscribe({
      next: (result) => console.log('📡 HTTP request completed:', result),
      error: (err) => console.error('📡 HTTP request failed:', err),
      complete: () => console.log('📡 HTTP request stream completed')
    });
  }

  logout(): void {
    this.isLoadingSignal.set(true);

    this.http.post(`${environment.apiUrl}/auth/logout/`, {}, {
      withCredentials: true
    }).pipe(
      tap(() => {
        this.userSignal.set(null);
        this.errorSignal.set(null);
        this.isLoadingSignal.set(false);
        this.router.navigate(['/login']);
      }),
      catchError(error => {
        // Even if logout fails on server, clear local state
        this.userSignal.set(null);
        this.errorSignal.set(null);
        this.isLoadingSignal.set(false);
        this.router.navigate(['/login']);
        return of(null);
      })
    ).subscribe();
  }

  checkAuthStatus(): void {
    this.isLoadingSignal.set(true);

    this.http.get<User>(`${environment.apiUrl}/auth/user/`, {
      withCredentials: true
    }).pipe(
      tap(user => {
        console.log('Auth status check result:', user);
        this.userSignal.set(user);
        this.errorSignal.set(null);
        this.isLoadingSignal.set(false);
      }),
      catchError(error => {
        console.log('Auth status check error:', error);
        // User is not authenticated, this is expected
        this.userSignal.set(null);
        this.errorSignal.set(null);
        this.isLoadingSignal.set(false);
        return of(null);
      })
    ).subscribe();
  }
}
