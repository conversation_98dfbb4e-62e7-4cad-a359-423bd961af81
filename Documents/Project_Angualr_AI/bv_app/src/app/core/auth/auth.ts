import { Injectable, signal, computed, inject, effect } from '@angular/core';
import { httpResource } from '@angular/common/http';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  success: boolean;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class Auth {
  private router = inject(Router);

  // Signal-based state management
  private userSignal = signal<User | null>(null);
  private loginCredentialsSignal = signal<LoginCredentials | null>(null);
  private shouldCheckAuthSignal = signal<boolean>(true);

  // Resource for login
  private loginResource = httpResource<LoginResponse>(() => {
    const credentials = this.loginCredentialsSignal();
    return credentials ? {
      url: `${environment.apiUrl}/auth/login/`,
      method: 'POST',
      body: credentials,
      withCredentials: true
    } : undefined;
  });

  // Resource for auth status check
  private authStatusResource = httpResource<User>(() =>
    this.shouldCheckAuthSignal() ? {
      url: `${environment.apiUrl}/auth/user/`,
      withCredentials: true
    } : undefined
  );

  // Resource for logout
  private logoutTriggerSignal = signal<boolean>(false);
  private logoutResource = httpResource<{success: boolean}>(() =>
    this.logoutTriggerSignal() ? {
      url: `${environment.apiUrl}/auth/logout/`,
      method: 'POST',
      body: {},
      withCredentials: true
    } : undefined
  );

  // Public readonly signals
  readonly user = this.userSignal.asReadonly();
  readonly isAuthenticated = computed(() => this.userSignal() !== null);
  readonly isLoading = computed(() =>
    this.loginResource.isLoading() ||
    this.authStatusResource.isLoading() ||
    this.logoutResource.isLoading()
  );
  readonly error = computed(() =>
    this.loginResource.error()?.message ||
    this.authStatusResource.error()?.message ||
    this.logoutResource.error()?.message ||
    null
  );

  constructor() {
    // Effect to handle login success
    effect(() => {
      const loginResult = this.loginResource.value();
      console.log('Login result:', loginResult);
      if (loginResult?.success && loginResult.user) {
        console.log('Login successful, setting user:', loginResult.user);
        this.userSignal.set(loginResult.user);
        this.loginCredentialsSignal.set(null); // Clear credentials
        this.router.navigate(['/dashboard']);
      }
    });

    // Effect to handle auth status check
    effect(() => {
      if (this.authStatusResource.hasValue()) {
        const user = this.authStatusResource.value();
        console.log('Auth status check result:', user);
        if (user) {
          this.userSignal.set(user);
        }
        // Stop checking auth status after first attempt
        this.shouldCheckAuthSignal.set(false);
      } else if (this.authStatusResource.error()) {
        console.log('Auth status check error:', this.authStatusResource.error());
        // Stop checking auth status on error
        this.shouldCheckAuthSignal.set(false);
      }
    });

    // Effect to handle logout
    effect(() => {
      const logoutResult = this.logoutResource.value();
      if (logoutResult?.success || this.logoutResource.error()) {
        this.userSignal.set(null);
        this.logoutTriggerSignal.set(false); // Reset trigger
        this.router.navigate(['/login']);
      }
    });

    // Initial auth status check
    this.checkAuthStatus();
  }

  login(credentials: LoginCredentials): void {
    console.log('Login called with credentials:', credentials);
    this.loginCredentialsSignal.set(credentials);
  }

  logout(): void {
    this.logoutTriggerSignal.set(true);
  }

  checkAuthStatus(): void {
    this.shouldCheckAuthSignal.set(true);
  }
}
